<template>
  <div class="modal fade" tabindex="-1" id="modalMentoria" ref="modalMentoria">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content mentoria-modal">
        <div class="modal-header bg-gradient-primary">
          <h5 class="modal-title d-flex align-items-center text-white">
            <i class="fas fa-handshake-angle me-2 text-white"></i>
            Mentoria - {{ mentoria?.paciente?.nome || 'Carregando...' }}
          </h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            data-bs-dismiss="modal"
            aria-label="Close"
            style="filter: brightness(0) invert(1);"
          ></button>
        </div>
        
        <div class="modal-body p-0" v-if="mentoria">
          <div class="row g-0 h-100">
            <!-- <PERSON><PERSON> - Informações da Mentoria -->
            <div class="col-md-5 bg-light border-end">
              <div class="p-4">
                <div class="mentoria-info">
                  <div class="info-section mb-4">
                    <h6 class="text-primary mb-3">
                      <i class="fas fa-info-circle me-2"></i>
                      Informações da Solicitação
                    </h6>
                    
                    <div class="info-item mb-3">
                      <label class="form-label text-muted small">Data da Solicitação:</label>
                      <div class="fw-bold">{{ $filters.dateTime(mentoria.created_at) }}</div>
                    </div>

                    <div class="info-item mb-3">
                      <label class="form-label text-muted small">Paciente:</label>
                      <div class="d-flex align-items-center">
                        <span class="fw-bold me-2">{{ mentoria.paciente.nome }}</span>
                        <button 
                          class="btn btn-sm btn-outline-primary"
                          @click="abrirPaciente"
                          title="Abrir prontuário do paciente"
                        >
                          <i class="fas fa-external-link-alt"></i>
                        </button>
                      </div>
                    </div>

                    <div class="info-item mb-3">
                      <label class="form-label text-muted small">Solicitante:</label>
                      <div class="fw-bold">{{ mentoria.solicitante.nome }}</div>
                    </div>

                    <div class="info-item mb-3">
                      <label class="form-label text-muted small">Clínica:</label>
                      <div class="fw-bold">{{ mentoria.paciente.clinica.nome }}</div>
                    </div>

                    <div class="info-item mb-3">
                      <label class="form-label text-muted small">Status:</label>
                      <div class="fw-bold">
                        <span class="badge" :class="getStatusClass(mentoria.status)">
                          {{ getStatusText(mentoria.status) }}
                        </span>
                      </div>
                    </div>

                    <div class="info-item">
                      <label class="form-label text-muted small">Observações Iniciais:</label>
                      <div class="observacoes-box p-3 bg-white border rounded">
                        {{ mentoria.observacao || 'Nenhuma observação fornecida.' }}
                      </div>
                    </div>

                    <!-- Botões de Ação -->
                    <div class="acoes-mentoria mt-4" v-if="podeInteragir()">
                      <div class="d-grid gap-2">
                        <!-- Botão Iniciar Mentoria (apenas para admins) -->
                        <button
                          v-if="$user?.system_admin && mentoria.status === 'AGUARDANDO'"
                          class="btn btn-success"
                          @click="iniciarMentoria"
                          :disabled="processandoAcao"
                        >
                          <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-2"></span>
                          <i v-else class="fas fa-play me-2"></i>
                          Iniciar Mentoria
                        </button>

                        <!-- Botão Finalizar -->
                        <button
                          v-if="mentoria.status === 'EM_ANDAMENTO'"
                          class="btn btn-primary"
                          @click="finalizarMentoria"
                          :disabled="processandoAcao"
                        >
                          <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-2"></span>
                          <i v-else class="fas fa-check me-2"></i>
                          Finalizar Mentoria
                        </button>

                        <!-- Botão Cancelar -->
                        <button
                          v-if="mentoria.status === 'AGUARDANDO' || mentoria.status === 'EM_ANDAMENTO'"
                          class="btn btn-outline-danger"
                          @click="cancelarMentoria"
                          :disabled="processandoAcao"
                        >
                          <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-2"></span>
                          <i v-else class="fas fa-times me-2"></i>
                          Cancelar Mentoria
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Lado Direito - Sistema de Mensagens -->
            <div class="col-md-7 d-flex flex-column">
              <div class="mensagens-header p-3 bg-white border-bottom">
                <h6 class="mb-0 text-primary">
                  <i class="fas fa-comments me-2"></i>
                  Conversas da Mentoria
                </h6>
              </div>

              <!-- Área de Mensagens -->
              <div class="mensagens-container flex-grow-1 p-3" ref="mensagensContainer">
                <div v-if="mensagens.length === 0" class="text-center text-muted py-5">
                  <i class="fas fa-comment-slash fa-2x mb-3"></i>
                  <p>Nenhuma mensagem ainda. Inicie a conversa!</p>
                </div>

                <div v-for="mensagem in mensagens" :key="mensagem.id" class="mensagem-item mb-3">
                  <!-- Mensagem do Sistema -->
                  <div v-if="mensagem.tipo === 'SISTEMA'" class="mensagem-sistema text-center">
                    <div class="sistema-badge">
                      <i class="fas fa-info-circle me-2"></i>
                      {{ mensagem.mensagem }}
                    </div>
                    <small class="text-muted d-block mt-1">
                      {{ $filters.dateTime(mensagem.created_at) }}
                    </small>
                  </div>

                  <!-- Mensagem de Usuário -->
                  <div v-else class="mensagem-usuario" :class="{ 'mensagem-direita': isMinhaMsg(mensagem.remetente_id) }">
                    <div
                      class="mensagem-card"
                      :class="{
                        'mensagem-minha': isMinhaMsg(mensagem.remetente_id),
                        'mensagem-outro': !isMinhaMsg(mensagem.remetente_id)
                      }"
                    >
                      <div class="mensagem-header d-flex align-items-center mb-2">
                        <div class="remetente-info d-flex align-items-center">
                          <i
                            v-if="isMentor(mensagem.remetente_id)"
                            class="fas fa-handshake-angle me-2"
                            title="Mentor"
                          ></i>
                          <i
                            v-else
                            class="fas fa-user me-2"
                            title="Solicitante"
                          ></i>
                          <span class="fw-bold">{{ mensagem.remetente?.nome || 'Usuário' }}</span>
                        </div>
                        <small class="text-muted ms-auto">
                          {{ $filters.dateTime(mensagem.created_at) }}
                        </small>
                      </div>
                      <div class="mensagem-conteudo">
                        {{ mensagem.mensagem }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Área de Digitação -->
              <div class="mensagem-input-area p-3 bg-light border-top">
                <form @submit.prevent="enviarMensagem" class="d-flex gap-2">
                  <div class="flex-grow-1">
                    <textarea
                      v-model="novaMensagem"
                      class="form-control"
                      placeholder="Digite sua mensagem..."
                      rows="2"
                      :disabled="enviandoMensagem"
                      @keydown.ctrl.enter="enviarMensagem"
                    ></textarea>
                    <small class="text-muted">Ctrl + Enter para enviar</small>
                  </div>
                  <div class="d-flex align-items-end">
                    <button
                      type="submit"
                      class="btn btn-primary"
                      :disabled="!novaMensagem.trim() || enviandoMensagem"
                    >
                      <span v-if="enviandoMensagem" class="spinner-border spinner-border-sm me-2"></span>
                      <i v-else class="fas fa-paper-plane me-2"></i>
                      Enviar
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-else class="modal-body text-center py-5">
          <div class="spinner-border text-primary" role="status"></div>
          <p class="mt-3 text-muted">Carregando mentoria...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getMentoria,
  enviarMensagem as enviarMensagemService,
  marcarMensagensComoLidas,
  iniciarMentoria as iniciarMentoriaService,
  finalizarMentoria as finalizarMentoriaService,
  cancelarMentoria as cancelarMentoriaService
} from '@/services/mentoriasService';
import { closeModal } from '@/utils/modalHelper';
import cSwal from '@/utils/cSwal.js';

export default {
  name: 'MentoriaModal',
  data() {
    return {
      mentoria: null,
      mensagens: [],
      novaMensagem: '',
      enviandoMensagem: false,
      processandoAcao: false,
      mentoriaId: null,
      intervalId: null,
    };
  },
  methods: {
    async abrirMentoria(mentoriaId) {
      this.mentoriaId = mentoriaId;
      this.mentoria = null;
      this.mensagens = [];
      
      try {
        const data = await getMentoria(mentoriaId);
        if (data) {
          this.mentoria = data;
          this.mensagens = data.mensagens || [];
          
          // Marcar mensagens como lidas
          await marcarMensagensComoLidas(mentoriaId);
          
          // Scroll para o final das mensagens
          this.$nextTick(() => {
            this.scrollToBottom();
          });

          // Iniciar polling para novas mensagens
          this.startPolling();
        } else {
          cSwal.cError('Erro ao carregar mentoria');
        }
      } catch (error) {
        console.error('Erro ao carregar mentoria:', error);
        cSwal.cError('Erro ao carregar mentoria');
      }
    },

    async enviarMensagem() {
      if (!this.novaMensagem.trim() || this.enviandoMensagem) return;

      this.enviandoMensagem = true;
      
      try {
        const sucesso = await enviarMensagemService(this.mentoriaId, this.novaMensagem.trim());
        
        if (sucesso) {
          this.novaMensagem = '';
          // Recarregar mensagens
          await this.recarregarMensagens();
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        } else {
          cSwal.cError('Erro ao enviar mensagem');
        }
      } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
        cSwal.cError('Erro ao enviar mensagem');
      } finally {
        this.enviandoMensagem = false;
      }
    },

    async recarregarMensagens() {
      if (!this.mentoriaId) return;
      
      try {
        const data = await getMentoria(this.mentoriaId);
        if (data) {
          this.mensagens = data.mensagens || [];
          await marcarMensagensComoLidas(this.mentoriaId);
        }
      } catch (error) {
        console.error('Erro ao recarregar mensagens:', error);
      }
    },

    isMinhaMsg(remetenteId) {
      // Verifica se a mensagem é minha (do usuário logado)
      const meuDentistaId = this.$user?.dentista?.id;
      return remetenteId === meuDentistaId;
    },

    isMentor(remetenteId) {
      // Verifica se o remetente é um mentor (não é o solicitante da mentoria)
      return remetenteId !== this.mentoria?.solicitante_id;
    },

    getStatusClass(status) {
      const classes = {
        'AGUARDANDO': 'badge-warning',
        'EM_ANDAMENTO': 'badge-primary',
        'FINALIZADA': 'badge-success',
        'CANCELADA': 'badge-danger'
      };
      return classes[status] || 'badge-secondary';
    },

    getStatusText(status) {
      const texts = {
        'AGUARDANDO': 'Aguardando',
        'EM_ANDAMENTO': 'Em Andamento',
        'FINALIZADA': 'Finalizada',
        'CANCELADA': 'Cancelada'
      };
      return texts[status] || status;
    },

    podeInteragir() {
      return true;
      // Verifica se o usuário pode interagir com a mentoria
      const meuDentistaId = this.$user?.dentista?.id;
      return this.$user?.system_admin || this.mentoria?.solicitante_id === meuDentistaId;
    },

    async iniciarMentoria() {
      if (!this.mentoriaId || this.processandoAcao) return;

      this.processandoAcao = true;

      try {
        const sucesso = await iniciarMentoriaService(this.mentoriaId);

        if (sucesso) {
          cSwal.cSuccess('Mentoria iniciada com sucesso!');
          await this.recarregarMensagens();
        } else {
          cSwal.cError('Erro ao iniciar mentoria');
        }
      } catch (error) {
        console.error('Erro ao iniciar mentoria:', error);
        cSwal.cError('Erro ao iniciar mentoria');
      } finally {
        this.processandoAcao = false;
      }
    },

    async finalizarMentoria() {
      if (!this.mentoriaId || this.processandoAcao) return;

      // Confirmar ação
      cSwal.cConfirm('Deseja realmente finalizar esta mentoria?', async () => {
        this.processandoAcao = true;

        try {
          const sucesso = await finalizarMentoriaService(this.mentoriaId);

          if (sucesso) {
            cSwal.cSuccess('Mentoria finalizada com sucesso!');
            await this.recarregarMensagens();
          } else {
            cSwal.cError('Erro ao finalizar mentoria');
          }
        } catch (error) {
          console.error('Erro ao finalizar mentoria:', error);
          cSwal.cError('Erro ao finalizar mentoria');
        } finally {
          this.processandoAcao = false;
        }
      });
    },

    async cancelarMentoria() {
      if (!this.mentoriaId || this.processandoAcao) return;

      // Confirmar ação
      cSwal.cConfirm('Deseja realmente cancelar esta mentoria?', async () => {
        this.processandoAcao = true;

        try {
          const sucesso = await cancelarMentoriaService(this.mentoriaId);

          if (sucesso) {
            cSwal.cSuccess('Mentoria cancelada com sucesso!');
            await this.recarregarMensagens();
          } else {
            cSwal.cError('Erro ao cancelar mentoria');
          }
        } catch (error) {
          console.error('Erro ao cancelar mentoria:', error);
          cSwal.cError('Erro ao cancelar mentoria');
        } finally {
          this.processandoAcao = false;
        }
      });
    },

    abrirPaciente() {
      if (this.mentoria?.paciente?.clinica?.slug && this.mentoria?.paciente?.id_ficha) {
        this.$router.push({
          name: "PacienteClinica",
          params: {
            clinica_slug: this.mentoria.paciente.clinica.slug,
            id_ficha: this.mentoria.paciente.id_ficha,
          }
        });
        // Fechar o modal
        closeModal('modalMentoria');
      }
    },

    scrollToBottom() {
      const container = this.$refs.mensagensContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },

    startPolling() {
      // Polling a cada 10 segundos para novas mensagens
      this.intervalId = setInterval(() => {
        this.recarregarMensagens();
      }, 10000);
    },

    stopPolling() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },

    fecharModal() {
      this.stopPolling();
      this.mentoria = null;
      this.mensagens = [];
      this.novaMensagem = '';
      this.mentoriaId = null;
    }
  },

  mounted() {
    // Escutar evento de fechamento do modal
    const modalElement = this.$refs.modalMentoria;
    if (modalElement) {
      modalElement.addEventListener('hidden.bs.modal', () => {
        this.fecharModal();
      });
    }
  },

  beforeUnmount() {
    this.fecharModal();
  }
};
</script>

<style scoped>
.mentoria-modal {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.mentoria-modal .modal-body {
  height: 70vh;
  max-height: 600px;
}

.observacoes-box {
  max-height: 120px;
  overflow-y: auto;
  font-size: 0.9rem;
  line-height: 1.4;
}

.mensagens-container {
  height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
}

.mensagem-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mensagem-card {
  padding: 12px 16px;
  border-radius: 12px;
  max-width: 85%;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.mensagem-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Mensagens do Sistema */
.mensagem-sistema {
  margin: 20px 0;
}

.sistema-badge {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  display: inline-block;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

/* Layout das mensagens */
.mensagem-usuario {
  display: flex;
  margin-bottom: 12px;
}

.mensagem-direita {
  justify-content: flex-end;
}

/* Mensagens minhas (à direita) */
.mensagem-minha {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.mensagem-minha .mensagem-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.mensagem-minha .remetente-info i {
  color: rgba(255, 255, 255, 0.9);
}

/* Mensagens dos outros (à esquerda) */
.mensagem-outro {
  background: white;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 4px;
}

.mensagem-outro .mensagem-header {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.mensagem-outro .remetente-info i {
  color: #6c757d;
}

.mensagem-conteudo {
  font-size: 0.95rem;
  line-height: 1.4;
  white-space: pre-wrap;
}

.mensagem-input-area {
  border-top: 2px solid #dee2e6;
}

.mensagem-input-area textarea {
  border-radius: 10px;
  border: 1px solid #ced4da;
  resize: none;
  transition: all 0.2s ease;
}

.mensagem-input-area textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.info-section {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.info-item {
  padding: 8px 0;
}

.info-item:not(:last-child) {
  border-bottom: 1px solid #f1f3f4;
}

.mensagens-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Botões de Ação */
.acoes-mentoria {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
}

.acoes-mentoria .btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.acoes-mentoria .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Status badges */
.badge-warning {
  background-color: #ffc107;
  color: #000;
}

.badge-primary {
  background-color: #0d6efd;
  color: #fff;
}

.badge-success {
  background-color: #198754;
  color: #fff;
}

.badge-danger {
  background-color: #dc3545;
  color: #fff;
}

/* Scrollbar personalizada */
.mensagens-container::-webkit-scrollbar {
  width: 6px;
}

.mensagens-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.mensagens-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.mensagens-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsividade */
@media (max-width: 768px) {
  .mentoria-modal .modal-body {
    height: 80vh;
  }

  .mensagem-card {
    max-width: 95%;
  }

  .col-md-5, .col-md-7 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .mensagens-container {
    height: 300px;
  }
}
</style>
